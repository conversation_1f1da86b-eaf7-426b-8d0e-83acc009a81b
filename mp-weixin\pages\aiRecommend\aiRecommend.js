// AI智能推荐页面
const { getApiUrl } = require('../../utils/config.js');

Page({
  data: {
    userInput: '', // 用户输入的需求
    isLoading: false, // 是否正在加载
    hasSearched: false, // 是否已经搜索过
    hasSelectedTags: false, // 是否选择了标签
    loadingDotIndex: 0, // 当前激活的加载点
    quickTags: [
      { id: 1, name: '清淡口味', selected: false },
      { id: 2, name: '麻辣口味', selected: false },
      { id: 3, name: '素食主义', selected: false },
      { id: 4, name: '低卡健康', selected: false },
      { id: 5, name: '下饭菜', selected: false },
      { id: 6, name: '汤品类', selected: false },
      { id: 7, name: '老人适宜', selected: false },
      { id: 8, name: '儿童喜爱', selected: false }
    ],
    recommendResult: [], // 推荐结果
    // 菜品详情弹窗相关数据
    openDetailPop: false, // 是否显示菜品详情弹窗
    dishDetailes: {}, // 当前查看的菜品详情
    dishMealData: [] // 套餐菜品数据
  },

  onLoad: function(options) {
    // 页面加载时的初始化
    console.log('AI推荐页面加载');
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 输入框内容变化
  onInputChange: function(e) {
    this.setData({
      userInput: e.detail.value
    });
  },

  // 选择快捷标签
  selectTag: function(e) {
    const index = e.currentTarget.dataset.index;
    const quickTags = this.data.quickTags;
    quickTags[index].selected = !quickTags[index].selected;
    
    // 检查是否有选中的标签
    const hasSelectedTags = quickTags.some(tag => tag.selected);
    
    this.setData({
      quickTags: quickTags,
      hasSelectedTags: hasSelectedTags
    });
  },

  // 获取AI推荐
  getRecommendation: function() {
    if (this.data.userInput.length === 0 && !this.data.hasSelectedTags) {
      wx.showToast({
        title: '请输入需求或选择标签',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isLoading: true,
      hasSearched: true
    });

    // 启动加载动画
    this.startLoadingAnimation();

    // 调用后端AI推荐API
    this.callBackendAI();
  },

  // 调用后端AI推荐API
  callBackendAI: function() {
    const { userInput, quickTags } = this.data;
    const selectedTags = quickTags.filter(tag => tag.selected).map(tag => tag.name);

    // 构建请求参数
    const requestData = {
      userInput: userInput || '',
      selectedTags: selectedTags,
      limit: 5
    };

    console.log('调用后端AI推荐API，请求参数：', requestData);

    // 使用配置化的API调用方式
    wx.request({
      url: getApiUrl('aiRecommend'), // 使用配置文件中的URL
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('后端AI推荐响应：', res);
        this.handleBackendResponse(res);
      },
      fail: (err) => {
        console.error('后端AI推荐调用失败：', err);
        this.handleBackendError(err);
      }
    });
  },

  // 处理后端响应
  handleBackendResponse: function(res) {
    try {
      if (res.statusCode === 200 && res.data && res.data.code === 1) {
        const recommendations = res.data.data;
        console.log('AI推荐结果：', recommendations);

        // 转换数据格式以适配前端显示
        const formattedRecommendations = recommendations.map(item => ({
          id: item.dishId,
          name: item.name,
          description: item.description,
          price: item.price,
          image: item.image || '/static/dish1.jpg', // 默认图片
          recommendReason: item.recommendReason,
          score: item.score || 90,
          tags: [item.categoryName || '美食'] // 使用分类名作为标签
        }));

        this.setData({
          isLoading: false,
          recommendResult: formattedRecommendations
        });

        // 停止加载动画
        this.stopLoadingAnimation();
      } else {
        throw new Error(res.data?.msg || '推荐服务返回异常');
      }
    } catch (error) {
      console.error('处理后端响应失败：', error);
      this.setData({
        isLoading: false
      });

      // 停止加载动画
      this.stopLoadingAnimation();

      wx.showToast({
        title: '推荐服务暂时不可用',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 处理后端调用错误
  handleBackendError: function(err) {
    console.error('后端调用失败:', err);
    this.setData({
      isLoading: false
    });

    // 停止加载动画
    this.stopLoadingAnimation();

    wx.showToast({
      title: '网络连接失败，请稍后重试',
      icon: 'none',
      duration: 2000
    });
  },



  // 查看菜品详情
  viewDishDetail: function(e) {
    const dish = e.currentTarget.dataset.dish;
    wx.showModal({
      title: dish.name,
      content: dish.description,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 打开菜品详情弹窗（参考首页的 openDetailHandle 方法）
  openDetailHandle: function(item) {
    console.log('打开菜品详情:', item);
    this.setData({
      dishDetailes: item
    });

    if (item.type === 2) {
      // 如果是套餐，需要查询套餐菜品详情
      // 这里需要调用 querySetmealDishById API
      // 暂时先显示弹窗，后续可以集成API
      this.setData({
        openDetailPop: true,
        dishMealData: [] // 套餐数据暂时为空
      });
    } else {
      // 普通菜品直接显示弹窗
      this.setData({
        openDetailPop: true
      });
    }
  },

  // 加入购物车（修改为打开菜品详情）
  addToCart: function(e) {
    const dish = e.currentTarget.dataset.dish;

    // 阻止事件冒泡
    e.stopPropagation();

    // 转换数据格式以适配菜品详情弹窗
    const dishDetail = {
      id: dish.id,
      name: dish.name,
      description: dish.description,
      price: dish.price,
      image: dish.image,
      type: 1, // 假设AI推荐的都是普通菜品
      flavors: [], // AI推荐暂时不考虑口味
      dishNumber: 0 // 初始数量为0
    };

    // 打开菜品详情弹窗
    this.openDetailHandle(dishDetail);
  },

  // 关闭菜品详情弹窗
  closeDetailPop: function() {
    this.setData({
      openDetailPop: false
    });
  },

  // 菜品加入购物车操作（在弹窗中使用）
  addDishAction: function(e) {
    const dish = this.data.dishDetailes;

    // 这里应该调用实际的加入购物车API
    // 目前只是显示提示
    wx.showToast({
      title: `${dish.name} 已加入购物车`,
      icon: 'success'
    });

    // 关闭弹窗
    this.closeDetailPop();

    // TODO: 集成实际的加入购物车API
    console.log('加入购物车:', dish);
  },

  // 菜品数量减少操作（在弹窗中使用）
  redDishAction: function(e) {
    const dishDetailes = this.data.dishDetailes;
    if (dishDetailes.dishNumber > 0) {
      dishDetailes.dishNumber--;
      this.setData({
        dishDetailes: dishDetailes
      });
    }
  },

  // 分享功能
  onShareAppMessage: function() {
    return {
      title: 'AI智能推荐 - 发现更适合你的美食',
      path: '/pages/aiRecommend/aiRecommend'
    };
  },

  // 启动加载动画
  startLoadingAnimation: function() {
    this.dotTimer = setInterval(() => {
      const currentIndex = this.data.loadingDotIndex;
      const nextIndex = (currentIndex + 1) % 5;
      this.setData({
        loadingDotIndex: nextIndex
      });
    }, 400);
  },

  // 停止加载动画
  stopLoadingAnimation: function() {
    if (this.dotTimer) {
      clearInterval(this.dotTimer);
      this.dotTimer = null;
    }
    this.setData({
      loadingDotIndex: 0
    });
  },

  // 页面卸载时清理
  onUnload: function() {
    this.stopLoadingAnimation();
  }
});
